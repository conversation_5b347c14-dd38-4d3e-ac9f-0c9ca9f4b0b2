"""
Bio-inspired adaptive feedback mechanism for QEPO system.

This module implements an immune-system inspired adaptive mechanism that
iteratively adjusts optimization weights for stressed or under-covered regions,
similar to how biological immune systems adapt to threats.
"""

import numpy as np
import time
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
import copy

from .models import Field, OptimizationResult
from .qubo_formulation import QUBOFormulator
from .quantum_solver import QuantumSolver, SolverConfig


@dataclass
class AdaptiveConfig:
    """Configuration for adaptive feedback mechanism."""
    max_iterations: int = 3
    stress_threshold: float = 0.7
    coverage_threshold: float = 0.3
    adaptation_rate: float = 0.5
    memory_decay: float = 0.8
    convergence_tolerance: float = 0.01


class ImmuneMemory:
    """
    Immune-inspired memory system for tracking problematic regions.
    
    Maintains memory of nodes that consistently show poor coverage or high stress,
    similar to how immune systems remember pathogens.
    """
    
    def __init__(self, decay_rate: float = 0.8):
        """Initialize immune memory with decay rate."""
        self.memory: Dict[str, float] = {}  # node_id -> stress memory
        self.decay_rate = decay_rate
        self.iteration_count = 0
    
    def update_memory(self, stressed_nodes: List[str], under_covered_nodes: List[str]):
        """Update memory with current stressed and under-covered nodes."""
        self.iteration_count += 1
        
        # Decay existing memory
        for node_id in self.memory:
            self.memory[node_id] *= self.decay_rate
        
        # Add new stress memories
        for node_id in stressed_nodes:
            current_memory = self.memory.get(node_id, 0.0)
            self.memory[node_id] = min(1.0, current_memory + 0.3)
        
        for node_id in under_covered_nodes:
            current_memory = self.memory.get(node_id, 0.0)
            self.memory[node_id] = min(1.0, current_memory + 0.2)
        
        # Remove very low memory values
        self.memory = {k: v for k, v in self.memory.items() if v > 0.05}
    
    def get_memory_adjustments(self) -> Dict[str, float]:
        """Get current memory-based weight adjustments."""
        return self.memory.copy()


class AdaptiveFeedbackEngine:
    """
    Bio-inspired adaptive feedback engine for iterative optimization improvement.
    
    Uses immune-system inspired mechanisms to identify and adapt to problematic
    regions in the field, improving optimization results over multiple iterations.
    """
    
    def __init__(self, config: Optional[AdaptiveConfig] = None):
        """Initialize adaptive feedback engine."""
        self.config = config or AdaptiveConfig()
        self.immune_memory = ImmuneMemory(self.config.memory_decay)
        self.iteration_history: List[Dict] = []
    
    def identify_problematic_nodes(
        self, 
        field: Field, 
        solution: np.ndarray,
        formulator: QUBOFormulator
    ) -> Tuple[List[str], List[str]]:
        """
        Identify nodes that are stressed or under-covered.
        
        Returns:
            Tuple of (stressed_nodes, under_covered_nodes)
        """
        stressed_nodes = []
        under_covered_nodes = []
        
        for i, node_id in enumerate(formulator.node_ids):
            node = field.nodes[node_id]
            is_selected = solution[i] == 1
            
            # Check for high environmental stress
            stress_factor = node.environmental_conditions.get_stress_factor()
            if stress_factor > self.config.stress_threshold:
                stressed_nodes.append(node_id)
            
            # Check for under-coverage of high-value nodes
            yield_potential = node.get_yield_potential()
            if not is_selected and yield_potential > self.config.coverage_threshold:
                under_covered_nodes.append(node_id)
        
        return stressed_nodes, under_covered_nodes
    
    def calculate_weight_adjustments(
        self, 
        stressed_nodes: List[str], 
        under_covered_nodes: List[str],
        formulator: QUBOFormulator
    ) -> Dict[str, float]:
        """
        Calculate weight adjustments for problematic nodes.
        
        Uses both current iteration feedback and immune memory.
        """
        adjustments = {}
        
        # Get memory-based adjustments
        memory_adjustments = self.immune_memory.get_memory_adjustments()
        
        # Current iteration adjustments
        for node_id in stressed_nodes:
            current_adj = adjustments.get(node_id, 0.0)
            memory_adj = memory_adjustments.get(node_id, 0.0)
            
            # Reduce weight for stressed nodes (discourage selection)
            total_adjustment = -(self.config.adaptation_rate * 0.5 + memory_adj * 0.3)
            adjustments[node_id] = current_adj + total_adjustment
        
        for node_id in under_covered_nodes:
            current_adj = adjustments.get(node_id, 0.0)
            memory_adj = memory_adjustments.get(node_id, 0.0)
            
            # Increase weight for under-covered high-value nodes
            total_adjustment = self.config.adaptation_rate + memory_adj * 0.5
            adjustments[node_id] = current_adj + total_adjustment
        
        return adjustments
    
    def apply_weight_adjustments(
        self, 
        Q: np.ndarray, 
        adjustments: Dict[str, float],
        formulator: QUBOFormulator
    ) -> np.ndarray:
        """Apply weight adjustments to the QUBO matrix."""
        Q_adjusted = Q.copy()
        
        for node_id, adjustment in adjustments.items():
            if node_id in formulator.node_to_index:
                i = formulator.node_to_index[node_id]
                Q_adjusted[i, i] += adjustment
        
        return Q_adjusted
    
    def check_convergence(self, recent_objectives: List[float]) -> bool:
        """Check if optimization has converged."""
        if len(recent_objectives) < 2:
            return False
        
        # Check if improvement is below tolerance
        recent_improvement = abs(recent_objectives[-1] - recent_objectives[-2])
        return recent_improvement < self.config.convergence_tolerance
    
    def adaptive_optimize(
        self, 
        field: Field,
        solver_config: Optional[SolverConfig] = None,
        formulation_params: Optional[Dict] = None
    ) -> OptimizationResult:
        """
        Run adaptive optimization with iterative feedback.
        
        Args:
            field: Field to optimize
            solver_config: Configuration for quantum solver
            formulation_params: Parameters for QUBO formulation
        
        Returns:
            OptimizationResult with adaptive improvements
        """
        
        start_time = time.time()
        
        # Initialize components
        formulator = QUBOFormulator(field)
        solver = QuantumSolver(solver_config or self.config.__dict__)
        
        if formulation_params is None:
            formulation_params = {}
        
        # Initial QUBO formulation
        Q_base, qubo_params = formulator.formulate_qubo(**formulation_params)
        
        best_solution = None
        best_objective = float('-inf')
        all_adjustments = {}
        objective_history = []
        
        for iteration in range(self.config.max_iterations):
            print(f"Adaptive iteration {iteration + 1}/{self.config.max_iterations}")
            
            # Apply accumulated adjustments
            Q_current = self.apply_weight_adjustments(Q_base, all_adjustments, formulator)
            
            # Solve current QUBO
            solution, objective_value, solver_info = solver.solve_qubo(Q_current)
            
            # Convert to maximization objective
            maximization_objective = -objective_value
            objective_history.append(maximization_objective)
            
            # Track best solution
            if maximization_objective > best_objective:
                best_objective = maximization_objective
                best_solution = solution.copy()
            
            # Identify problematic nodes
            stressed_nodes, under_covered_nodes = self.identify_problematic_nodes(
                field, solution, formulator
            )
            
            # Update immune memory
            self.immune_memory.update_memory(stressed_nodes, under_covered_nodes)
            
            # Calculate new adjustments
            new_adjustments = self.calculate_weight_adjustments(
                stressed_nodes, under_covered_nodes, formulator
            )
            
            # Accumulate adjustments
            for node_id, adj in new_adjustments.items():
                all_adjustments[node_id] = all_adjustments.get(node_id, 0.0) + adj
            
            # Store iteration info
            iteration_info = {
                'iteration': iteration + 1,
                'objective': maximization_objective,
                'stressed_nodes': len(stressed_nodes),
                'under_covered_nodes': len(under_covered_nodes),
                'total_adjustments': len(all_adjustments),
                'solver_info': solver_info
            }
            self.iteration_history.append(iteration_info)
            
            # Check convergence
            if self.check_convergence(objective_history):
                print(f"Converged after {iteration + 1} iterations")
                break
            
            # Early stopping if no problematic nodes
            if not stressed_nodes and not under_covered_nodes:
                print(f"No problematic nodes found after {iteration + 1} iterations")
                break
        
        total_time = time.time() - start_time
        
        # Create final result
        result = OptimizationResult(
            field=field,
            optimal_solution=best_solution,
            objective_value=best_objective,
            iterations=len(self.iteration_history),
            computation_time=total_time,
            quantum_energy=solver_info.get('energy', best_objective),
            stress_adjustments=all_adjustments
        )
        
        return result
    
    def get_adaptation_summary(self) -> Dict:
        """Get summary of adaptive feedback process."""
        if not self.iteration_history:
            return {}
        
        objectives = [info['objective'] for info in self.iteration_history]
        
        return {
            'total_iterations': len(self.iteration_history),
            'initial_objective': objectives[0],
            'final_objective': objectives[-1],
            'improvement': objectives[-1] - objectives[0],
            'improvement_percentage': ((objectives[-1] - objectives[0]) / abs(objectives[0])) * 100,
            'convergence_achieved': len(objectives) < self.config.max_iterations,
            'memory_size': len(self.immune_memory.memory),
            'iteration_history': self.iteration_history
        }
