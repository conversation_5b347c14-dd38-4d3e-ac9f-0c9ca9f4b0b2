"""
Plotting utilities for QEPO visualization.

This module provides functions for creating various plots and visualizations
of the pollination optimization results.
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from typing import Dict, List, Optional, Tuple

from ..core.models import Field, OptimizationResult, FloweringStage, CropType


def create_field_heatmap(
    field: Field, 
    solution: Optional[np.ndarray] = None,
    metric: str = "yield_potential"
) -> go.Figure:
    """
    Create a heatmap visualization of the field.
    
    Args:
        field: Field object to visualize
        solution: Optional binary solution array to highlight selected nodes
        metric: Metric to visualize ("yield_potential", "stress", "pollination_prob")
    
    Returns:
        Plotly figure object
    """
    
    # Extract node data
    nodes_data = []
    for node_id, node in field.nodes.items():
        if metric == "yield_potential":
            value = node.get_yield_potential()
            colorscale = "Viridis"
            title = "Yield Potential"
        elif metric == "stress":
            value = node.environmental_conditions.get_stress_factor()
            colorscale = "Reds"
            title = "Environmental Stress"
        elif metric == "pollination_prob":
            value = node.get_effective_pollination_probability()
            colorscale = "Blues"
            title = "Pollination Probability"
        else:
            value = 1.0
            colorscale = "Greys"
            title = "Field Layout"
        
        # Determine if node is selected
        is_selected = False
        if solution is not None:
            node_index = list(field.nodes.keys()).index(node_id)
            is_selected = solution[node_index] == 1
        
        nodes_data.append({
            'x': node.x,
            'y': node.y,
            'value': value,
            'node_id': node_id,
            'crop_type': node.crop_type.value,
            'flowering_stage': node.flowering_stage.value,
            'selected': is_selected,
            'marker_size': 15 if is_selected else 10,
            'marker_symbol': 'star' if is_selected else 'circle'
        })
    
    df = pd.DataFrame(nodes_data)
    
    # Create scatter plot
    fig = px.scatter(
        df, 
        x='x', 
        y='y', 
        color='value',
        size='marker_size',
        symbol='marker_symbol',
        color_continuous_scale=colorscale,
        hover_data=['node_id', 'crop_type', 'flowering_stage', 'selected'],
        title=f"Field {title} Heatmap"
    )
    
    # Update layout
    fig.update_layout(
        width=800,
        height=600,
        xaxis_title="X Position (m)",
        yaxis_title="Y Position (m)",
        coloraxis_colorbar_title=title
    )
    
    return fig


def create_optimization_progress_plot(result: OptimizationResult) -> go.Figure:
    """Create a plot showing optimization progress over iterations."""
    
    # This would need iteration history from adaptive feedback
    # For now, create a simple result summary
    
    metrics = {
        'Coverage': result.get_coverage_percentage(),
        'Yield Improvement': result.get_yield_improvement(),
        'Computation Time': result.computation_time
    }
    
    fig = go.Figure(data=[
        go.Bar(
            x=list(metrics.keys()),
            y=list(metrics.values()),
            text=[f"{v:.2f}" for v in metrics.values()],
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title="Optimization Results Summary",
        yaxis_title="Value",
        width=600,
        height=400
    )
    
    return fig


def create_network_visualization(field: Field, solution: Optional[np.ndarray] = None) -> go.Figure:
    """
    Create a network visualization showing nodes and edges.
    
    Args:
        field: Field object to visualize
        solution: Optional solution to highlight selected nodes and edges
    
    Returns:
        Plotly figure with network layout
    """
    
    # Prepare node data
    node_x = []
    node_y = []
    node_text = []
    node_colors = []
    node_sizes = []
    
    node_ids = list(field.nodes.keys())
    
    for i, (node_id, node) in enumerate(field.nodes.items()):
        node_x.append(node.x)
        node_y.append(node.y)
        
        # Node info for hover
        info = f"ID: {node_id}<br>"
        info += f"Crop: {node.crop_type.value}<br>"
        info += f"Stage: {node.flowering_stage.value}<br>"
        info += f"Yield: {node.get_yield_potential():.2f}"
        
        if solution is not None and solution[i] == 1:
            info += "<br>SELECTED"
            node_colors.append('red')
            node_sizes.append(20)
        else:
            node_colors.append('lightblue')
            node_sizes.append(10)
        
        node_text.append(info)
    
    # Prepare edge data
    edge_x = []
    edge_y = []
    
    for edge in field.edges:
        source_node = field.nodes[edge.source_id]
        target_node = field.nodes[edge.target_id]
        
        edge_x.extend([source_node.x, target_node.x, None])
        edge_y.extend([source_node.y, target_node.y, None])
    
    # Create figure
    fig = go.Figure()
    
    # Add edges
    fig.add_trace(go.Scatter(
        x=edge_x, y=edge_y,
        mode='lines',
        line=dict(width=0.5, color='gray'),
        hoverinfo='none',
        showlegend=False,
        name='Connections'
    ))
    
    # Add nodes
    fig.add_trace(go.Scatter(
        x=node_x, y=node_y,
        mode='markers',
        marker=dict(
            size=node_sizes,
            color=node_colors,
            line=dict(width=2, color='black')
        ),
        text=node_text,
        hoverinfo='text',
        name='Flower Clusters'
    ))
    
    fig.update_layout(
        title="Pollination Network Visualization",
        xaxis_title="X Position (m)",
        yaxis_title="Y Position (m)",
        showlegend=True,
        width=800,
        height=600,
        hovermode='closest'
    )
    
    return fig


def create_environmental_dashboard(field: Field) -> go.Figure:
    """Create dashboard showing environmental conditions across the field."""
    
    # Extract environmental data
    env_data = []
    for node_id, node in field.nodes.items():
        env = node.environmental_conditions
        env_data.append({
            'node_id': node_id,
            'x': node.x,
            'y': node.y,
            'temperature': env.temperature,
            'humidity': env.humidity,
            'wind_speed': env.wind_speed,
            'rainfall': env.rainfall,
            'pest_pressure': env.pest_pressure,
            'stress_factor': env.get_stress_factor()
        })
    
    df = pd.DataFrame(env_data)
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=['Temperature', 'Humidity', 'Wind Speed', 
                       'Rainfall', 'Pest Pressure', 'Overall Stress'],
        specs=[[{'type': 'scatter'}, {'type': 'scatter'}, {'type': 'scatter'}],
               [{'type': 'scatter'}, {'type': 'scatter'}, {'type': 'scatter'}]]
    )
    
    metrics = ['temperature', 'humidity', 'wind_speed', 'rainfall', 'pest_pressure', 'stress_factor']
    positions = [(1,1), (1,2), (1,3), (2,1), (2,2), (2,3)]
    
    for metric, (row, col) in zip(metrics, positions):
        fig.add_trace(
            go.Scatter(
                x=df['x'],
                y=df['y'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=df[metric],
                    colorscale='RdYlBu_r' if metric == 'stress_factor' else 'Viridis',
                    showscale=True if (row, col) == (2, 3) else False
                ),
                text=df['node_id'],
                name=metric.replace('_', ' ').title()
            ),
            row=row, col=col
        )
    
    fig.update_layout(
        title="Environmental Conditions Dashboard",
        height=800,
        showlegend=False
    )
    
    return fig


def create_yield_comparison_chart(
    baseline_yield: float,
    optimized_yield: float,
    scenarios: Optional[List[Tuple[str, float]]] = None
) -> go.Figure:
    """Create a comparison chart of yield improvements."""

    categories = ['Baseline (Random)', 'QEPO Optimized']
    values = [baseline_yield, optimized_yield]
    colors = ['lightcoral', 'lightgreen']

    if scenarios:
        for scenario_name, scenario_yield in scenarios:
            categories.append(f"Scenario: {scenario_name}")
            values.append(scenario_yield)
            colors.append('lightblue')

    fig = go.Figure(data=[
        go.Bar(
            x=categories,
            y=values,
            marker_color=colors,
            text=[f"{v:.2f}" for v in values],
            textposition='auto'
        )
    ])

    improvement = ((optimized_yield - baseline_yield) / baseline_yield) * 100

    fig.update_layout(
        title=f"Yield Comparison (Improvement: {improvement:.1f}%)",
        yaxis_title="Expected Yield",
        width=700,
        height=400
    )

    return fig


def create_adaptation_progress_plot(adaptation_history: List[Dict]) -> go.Figure:
    """Create a plot showing adaptive feedback progress."""

    if not adaptation_history:
        return go.Figure().add_annotation(
            text="No adaptation history available",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False
        )

    iterations = [info['iteration'] for info in adaptation_history]
    objectives = [info['objective'] for info in adaptation_history]
    stressed_counts = [info['stressed_nodes'] for info in adaptation_history]

    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=['Objective Value Progress', 'Stressed Nodes Count'],
        vertical_spacing=0.1
    )

    # Objective progress
    fig.add_trace(
        go.Scatter(
            x=iterations,
            y=objectives,
            mode='lines+markers',
            name='Objective Value',
            line=dict(color='blue', width=3)
        ),
        row=1, col=1
    )

    # Stressed nodes count
    fig.add_trace(
        go.Scatter(
            x=iterations,
            y=stressed_counts,
            mode='lines+markers',
            name='Stressed Nodes',
            line=dict(color='red', width=3)
        ),
        row=2, col=1
    )

    fig.update_layout(
        title="Adaptive Feedback Progress",
        height=600,
        showlegend=False
    )

    fig.update_xaxes(title_text="Iteration", row=2, col=1)
    fig.update_yaxes(title_text="Objective Value", row=1, col=1)
    fig.update_yaxes(title_text="Count", row=2, col=1)

    return fig
