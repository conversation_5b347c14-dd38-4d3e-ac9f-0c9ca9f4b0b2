"""
Tests for core data models.
"""

import pytest
import numpy as np
from qepo.core.models import (
    Node, Edge, Field, EnvironmentalConditions, 
    FloweringStage, CropType, OptimizationResult
)


class TestEnvironmentalConditions:
    """Test environmental conditions model."""
    
    def test_stress_factor_calculation(self):
        """Test stress factor calculation."""
        # Low stress conditions
        low_stress = EnvironmentalConditions(
            temperature=20.0,
            humidity=60.0,
            wind_speed=2.0,
            rainfall=0.0,
            pest_pressure=0.1
        )
        assert low_stress.get_stress_factor() < 0.3
        
        # High stress conditions
        high_stress = EnvironmentalConditions(
            temperature=35.0,  # Too hot
            humidity=90.0,
            wind_speed=15.0,   # Too windy
            rainfall=10.0,     # Too much rain
            pest_pressure=0.9  # High pest pressure
        )
        assert high_stress.get_stress_factor() > 0.7


class TestNode:
    """Test node model."""
    
    def test_node_creation(self):
        """Test basic node creation."""
        env = EnvironmentalConditions(
            temperature=20.0, humidity=60.0, wind_speed=2.0,
            rainfall=0.0, pest_pressure=0.1
        )
        
        node = Node(
            id="test_node",
            x=100.0,
            y=200.0,
            crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.5,
            flower_density=0.8,
            environmental_conditions=env,
            base_pollination_probability=0.7
        )
        
        assert node.id == "test_node"
        assert node.crop_type == CropType.APPLE
        assert node.flowering_stage == FloweringStage.PEAK_BLOOM
    
    def test_effective_pollination_probability(self):
        """Test pollination probability calculation."""
        env = EnvironmentalConditions(
            temperature=20.0, humidity=60.0, wind_speed=2.0,
            rainfall=0.0, pest_pressure=0.1
        )
        
        # Peak bloom node should have high probability
        peak_node = Node(
            id="peak", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.9,
            environmental_conditions=env, base_pollination_probability=0.8
        )
        
        # Pre-bloom node should have low probability
        pre_node = Node(
            id="pre", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PRE_BLOOM,
            crop_importance=3.0, flower_density=0.9,
            environmental_conditions=env, base_pollination_probability=0.8
        )
        
        assert peak_node.get_effective_pollination_probability() > pre_node.get_effective_pollination_probability()
    
    def test_yield_potential(self):
        """Test yield potential calculation."""
        env = EnvironmentalConditions(
            temperature=20.0, humidity=60.0, wind_speed=2.0,
            rainfall=0.0, pest_pressure=0.1
        )
        
        high_importance_node = Node(
            id="high", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=5.0, flower_density=1.0,
            environmental_conditions=env, base_pollination_probability=1.0
        )
        
        low_importance_node = Node(
            id="low", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=1.0, flower_density=1.0,
            environmental_conditions=env, base_pollination_probability=1.0
        )
        
        assert high_importance_node.get_yield_potential() > low_importance_node.get_yield_potential()


class TestEdge:
    """Test edge model."""
    
    def test_edge_creation(self):
        """Test basic edge creation."""
        edge = Edge(
            source_id="node1",
            target_id="node2",
            distance=50.0,
            movement_probability=0.8
        )
        
        assert edge.source_id == "node1"
        assert edge.target_id == "node2"
        assert edge.distance == 50.0
    
    def test_effective_movement_probability(self):
        """Test movement probability calculation."""
        # Create test nodes
        env1 = EnvironmentalConditions(20.0, 60.0, 2.0, 0.0, 0.1)
        env2 = EnvironmentalConditions(22.0, 65.0, 3.0, 0.0, 0.2)
        
        node1 = Node(
            id="n1", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.8,
            environmental_conditions=env1, base_pollination_probability=0.7
        )
        
        node2 = Node(
            id="n2", x=100, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.8,
            environmental_conditions=env2, base_pollination_probability=0.7
        )
        
        # Short distance edge
        short_edge = Edge("n1", "n2", distance=50.0, movement_probability=0.8)
        
        # Long distance edge
        long_edge = Edge("n1", "n2", distance=200.0, movement_probability=0.8)
        
        short_prob = short_edge.get_effective_movement_probability(node1, node2)
        long_prob = long_edge.get_effective_movement_probability(node1, node2)
        
        assert short_prob > long_prob


class TestField:
    """Test field model."""
    
    def test_field_creation(self):
        """Test basic field creation."""
        field = Field(
            name="Test Field",
            field_width=1000.0,
            field_height=1000.0
        )
        
        assert field.name == "Test Field"
        assert len(field.nodes) == 0
        assert len(field.edges) == 0
    
    def test_add_nodes_and_edges(self):
        """Test adding nodes and edges to field."""
        field = Field(name="Test", field_width=1000.0, field_height=1000.0)
        
        # Create test nodes
        env = EnvironmentalConditions(20.0, 60.0, 2.0, 0.0, 0.1)
        
        node1 = Node(
            id="n1", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.8,
            environmental_conditions=env, base_pollination_probability=0.7
        )
        
        node2 = Node(
            id="n2", x=100, y=100, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.8,
            environmental_conditions=env, base_pollination_probability=0.7
        )
        
        field.add_node(node1)
        field.add_node(node2)
        
        assert len(field.nodes) == 2
        
        # Add edge
        edge = Edge("n1", "n2", distance=141.42, movement_probability=0.5)
        field.add_edge(edge)
        
        assert len(field.edges) == 1
    
    def test_adjacency_matrix(self):
        """Test adjacency matrix generation."""
        field = Field(name="Test", field_width=1000.0, field_height=1000.0)
        
        # Add two nodes
        env = EnvironmentalConditions(20.0, 60.0, 2.0, 0.0, 0.1)
        
        for i in range(2):
            node = Node(
                id=f"n{i}", x=i*100, y=0, crop_type=CropType.APPLE,
                flowering_stage=FloweringStage.PEAK_BLOOM,
                crop_importance=3.0, flower_density=0.8,
                environmental_conditions=env, base_pollination_probability=0.7
            )
            field.add_node(node)
        
        # Add edge
        edge = Edge("n0", "n1", distance=100.0, movement_probability=0.5)
        field.add_edge(edge)
        
        adj_matrix = field.get_adjacency_matrix()
        assert adj_matrix.shape == (2, 2)
        assert adj_matrix[0, 1] > 0  # Should have connection
        assert adj_matrix[1, 1] == 0  # No self-loop
    
    def test_stressed_nodes_identification(self):
        """Test identification of stressed nodes."""
        field = Field(name="Test", field_width=1000.0, field_height=1000.0)
        
        # Add stressed node
        stressed_env = EnvironmentalConditions(35.0, 90.0, 15.0, 10.0, 0.9)
        stressed_node = Node(
            id="stressed", x=0, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.8,
            environmental_conditions=stressed_env, base_pollination_probability=0.7
        )
        
        # Add normal node
        normal_env = EnvironmentalConditions(20.0, 60.0, 2.0, 0.0, 0.1)
        normal_node = Node(
            id="normal", x=100, y=0, crop_type=CropType.APPLE,
            flowering_stage=FloweringStage.PEAK_BLOOM,
            crop_importance=3.0, flower_density=0.8,
            environmental_conditions=normal_env, base_pollination_probability=0.7
        )
        
        field.add_node(stressed_node)
        field.add_node(normal_node)
        
        stressed_nodes = field.get_stressed_nodes(threshold=0.5)
        assert "stressed" in stressed_nodes
        assert "normal" not in stressed_nodes


if __name__ == "__main__":
    pytest.main([__file__])
