"""
Interactive Streamlit dashboard for QEPO system.

This module provides a web-based interface for farmers and researchers
to interact with the quantum pollination optimization system.
"""

import streamlit as st
import numpy as np
import pandas as pd
import time
from typing import Dict, Optional

# Import QEPO components
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from qepo.data.synthetic_generator import generate_field, generate_scenario_variations
from qepo.core.quantum_solver import optimize_pollination, SolverConfig
from qepo.core.adaptive_feedback import AdaptiveFeedbackEngine, AdaptiveConfig
from qepo.core.models import CropType, FloweringStage

# Try to import plotting functions, but provide fallbacks if they fail
try:
    from qepo.visualization.plotting import (
        create_field_heatmap,
        create_optimization_progress_plot,
        create_network_visualization,
        create_environmental_dashboard,
        create_yield_comparison_chart,
        create_adaptation_progress_plot
    )
    PLOTTING_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Plotting functions not available: {e}")
    PLOTTING_AVAILABLE = False


def main():
    """Main dashboard application."""
    
    st.set_page_config(
        page_title="QEPO - Quantum-Enhanced Pollination Optimization",
        page_icon="🌸",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🌸 Quantum-Enhanced Pollination Optimization (QEPO)")
    st.markdown("*Optimizing crop pollination using quantum computing and bio-inspired algorithms*")
    
    # Sidebar controls
    st.sidebar.header("🎛️ Configuration")
    
    # Field generation parameters
    st.sidebar.subheader("Field Parameters")
    grid_size = st.sidebar.slider("Grid Size", min_value=5, max_value=15, value=10)
    field_width = st.sidebar.number_input("Field Width (m)", min_value=500, max_value=2000, value=1000)
    field_height = st.sidebar.number_input("Field Height (m)", min_value=500, max_value=2000, value=1000)
    crop_diversity = st.sidebar.checkbox("Multiple Crop Types", value=True)
    stress_zones = st.sidebar.checkbox("Include Stress Zones", value=True)
    random_seed = st.sidebar.number_input("Random Seed", min_value=0, max_value=9999, value=42)
    
    # Optimization parameters
    st.sidebar.subheader("Optimization Parameters")
    solver_type = st.sidebar.selectbox(
        "Solver Type", 
        ["simulated_annealing", "classical"],
        index=0
    )
    
    use_adaptive = st.sidebar.checkbox("Use Adaptive Feedback", value=True)
    max_iterations = st.sidebar.slider("Max Iterations", min_value=1, max_value=10, value=3)
    
    # QUBO formulation parameters
    st.sidebar.subheader("QUBO Parameters")
    yield_weight = st.sidebar.slider("Yield Weight", min_value=0.1, max_value=3.0, value=1.0, step=0.1)
    movement_weight = st.sidebar.slider("Movement Weight", min_value=0.1, max_value=2.0, value=0.5, step=0.1)
    stress_penalty = st.sidebar.slider("Stress Penalty", min_value=0.5, max_value=5.0, value=2.0, step=0.1)
    
    # Generate field button
    if st.sidebar.button("🌱 Generate New Field", type="primary"):
        st.session_state.field_generated = True
        st.session_state.optimization_run = False
    
    # Initialize session state
    if 'field_generated' not in st.session_state:
        st.session_state.field_generated = False
    if 'optimization_run' not in st.session_state:
        st.session_state.optimization_run = False
    
    # Generate field
    if st.session_state.field_generated or st.sidebar.button("🌱 Generate Field"):
        with st.spinner("Generating synthetic field..."):
            field = generate_field(
                grid_size=grid_size,
                field_width=field_width,
                field_height=field_height,
                crop_diversity=crop_diversity,
                stress_zones=stress_zones,
                seed=random_seed
            )
            st.session_state.field = field
            st.session_state.field_generated = True
        
        st.success(f"Generated field with {len(field.nodes)} nodes and {len(field.edges)} connections")
        
        # Display field statistics
        stats = field.get_field_statistics()
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Nodes", stats['total_nodes'])
        with col2:
            st.metric("Total Connections", stats['total_edges'])
        with col3:
            st.metric("Avg Stress Factor", f"{stats['average_stress_factor']:.2f}")
        with col4:
            st.metric("Yield Potential", f"{stats['total_yield_potential']:.1f}")
        
        # Field visualization tabs
        tab1, tab2, tab3 = st.tabs(["🗺️ Field Layout", "🌡️ Environmental Conditions", "🕸️ Network View"])
        
        with tab1:
            fig_yield = create_field_heatmap(field, metric="yield_potential")
            st.plotly_chart(fig_yield, use_container_width=True)
        
        with tab2:
            fig_env = create_environmental_dashboard(field)
            st.plotly_chart(fig_env, use_container_width=True)
        
        with tab3:
            fig_network = create_network_visualization(field)
            st.plotly_chart(fig_network, use_container_width=True)
    
    # Optimization section
    if st.session_state.field_generated and hasattr(st.session_state, 'field'):
        st.header("🚀 Run Optimization")
        
        if st.button("🎯 Optimize Pollination", type="primary"):
            field = st.session_state.field
            
            # Configure solver
            solver_config = SolverConfig(
                solver_type=solver_type,
                num_reads=1000,
                max_iterations=100
            )
            
            # Configure formulation
            formulation_params = {
                'yield_weight': yield_weight,
                'movement_weight': movement_weight,
                'stress_penalty_weight': stress_penalty,
                'coverage_constraint': True,
                'flowering_bonus': True,
                'spatial_clustering': True
            }
            
            with st.spinner("Running quantum optimization..."):
                if use_adaptive:
                    # Use adaptive feedback
                    adaptive_config = AdaptiveConfig(max_iterations=max_iterations)
                    adaptive_engine = AdaptiveFeedbackEngine(adaptive_config)
                    
                    result = adaptive_engine.adaptive_optimize(
                        field, solver_config, formulation_params
                    )
                    
                    st.session_state.adaptation_summary = adaptive_engine.get_adaptation_summary()
                    st.session_state.adaptation_history = adaptive_engine.iteration_history
                else:
                    # Standard optimization
                    result = optimize_pollination(field, solver_config, formulation_params)
                    st.session_state.adaptation_summary = None
                    st.session_state.adaptation_history = []
                
                st.session_state.optimization_result = result
                st.session_state.optimization_run = True
            
            st.success("Optimization completed!")
    
    # Results section
    if st.session_state.optimization_run and hasattr(st.session_state, 'optimization_result'):
        result = st.session_state.optimization_result
        field = st.session_state.field
        
        st.header("📊 Optimization Results")
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Coverage", f"{result.get_coverage_percentage():.1f}%")
        with col2:
            st.metric("Yield Improvement", f"{result.get_yield_improvement():.1f}%")
        with col3:
            st.metric("Computation Time", f"{result.computation_time:.2f}s")
        with col4:
            st.metric("Iterations", result.iterations)
        
        # Results visualization tabs
        tab1, tab2, tab3, tab4 = st.tabs(["🎯 Optimized Layout", "📈 Progress", "🔄 Adaptation", "📋 Details"])
        
        with tab1:
            # Show optimized field layout
            fig_optimized = create_field_heatmap(field, result.optimal_solution, "yield_potential")
            st.plotly_chart(fig_optimized, use_container_width=True)
            
            # Yield comparison
            baseline_yield = field.calculate_total_yield_potential() * 0.5  # Assume 50% random coverage
            optimized_yield = sum(
                field.nodes[node_id].get_yield_potential() 
                for node_id in result.get_pollinated_nodes()
            )
            
            fig_comparison = create_yield_comparison_chart(baseline_yield, optimized_yield)
            st.plotly_chart(fig_comparison, use_container_width=True)
        
        with tab2:
            fig_progress = create_optimization_progress_plot(result)
            st.plotly_chart(fig_progress, use_container_width=True)
        
        with tab3:
            if hasattr(st.session_state, 'adaptation_history') and st.session_state.adaptation_history:
                fig_adaptation = create_adaptation_progress_plot(st.session_state.adaptation_history)
                st.plotly_chart(fig_adaptation, use_container_width=True)
                
                if hasattr(st.session_state, 'adaptation_summary'):
                    summary = st.session_state.adaptation_summary
                    st.subheader("Adaptation Summary")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Total Iterations", summary.get('total_iterations', 0))
                        st.metric("Initial Objective", f"{summary.get('initial_objective', 0):.2f}")
                    with col2:
                        st.metric("Final Objective", f"{summary.get('final_objective', 0):.2f}")
                        st.metric("Improvement", f"{summary.get('improvement_percentage', 0):.1f}%")
            else:
                st.info("Adaptive feedback was not used in this optimization.")
        
        with tab4:
            st.subheader("Detailed Results")
            
            # Selected nodes
            pollinated_nodes = result.get_pollinated_nodes()
            st.write(f"**Selected Nodes ({len(pollinated_nodes)}):**")
            st.write(", ".join(pollinated_nodes))
            
            # Stress adjustments
            if result.stress_adjustments:
                st.write("**Stress Adjustments:**")
                adj_df = pd.DataFrame([
                    {"Node ID": node_id, "Adjustment": adj}
                    for node_id, adj in result.stress_adjustments.items()
                ])
                st.dataframe(adj_df)
            
            # Raw optimization data
            with st.expander("Raw Data"):
                st.write("**Optimization Result Object:**")
                st.json({
                    "objective_value": result.objective_value,
                    "coverage_percentage": result.get_coverage_percentage(),
                    "yield_improvement": result.get_yield_improvement(),
                    "computation_time": result.computation_time,
                    "iterations": result.iterations
                })


if __name__ == "__main__":
    main()
