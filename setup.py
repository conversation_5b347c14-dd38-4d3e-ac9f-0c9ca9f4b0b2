"""
Setup script for QEPO (Quantum-Enhanced Pollination Optimization) package.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="qepo",
    version="0.1.0",
    author="QEPO Development Team",
    author_email="<EMAIL>",
    description="Quantum-Enhanced Pollination Optimization for Agriculture",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/qepo/qepo",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Agriculture",
        "Topic :: Scientific/Engineering :: Physics",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "quantum": [
            "dwave-ocean-sdk>=6.0.0",
            "qiskit>=0.44.0",
            "qiskit-optimization>=0.6.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "qepo-dashboard=qepo.visualization.dashboard:main",
            "qepo-example=examples.basic_usage:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
