"""
Core data models for the QEPO system.

This module defines the fundamental data structures for representing
pollination networks including nodes (flower clusters), edges (pollinator
movement probabilities), and fields (complete agricultural areas).
"""

from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from pydantic import BaseModel, Field as PydanticField, validator


class FloweringStage(Enum):
    """Flowering stages of crops."""
    PRE_BLOOM = "pre_bloom"
    EARLY_BLOOM = "early_bloom"
    PEAK_BLOOM = "peak_bloom"
    LATE_BLOOM = "late_bloom"
    POST_BLOOM = "post_bloom"


class CropType(Enum):
    """Types of crops that require pollination."""
    ALMOND = "almond"
    APPLE = "apple"
    BLUEBERRY = "blueberry"
    CHERRY = "cherry"
    STRAWBERRY = "strawberry"
    SUNFLOWER = "sunflower"
    CANOLA = "canola"


@dataclass
class EnvironmentalConditions:
    """Environmental conditions affecting pollination."""
    temperature: float  # Celsius
    humidity: float     # Percentage (0-100)
    wind_speed: float   # m/s
    rainfall: float     # mm/day
    pest_pressure: float  # Stress factor (0-1, where 1 = high stress)
    
    def get_stress_factor(self) -> float:
        """Calculate overall environmental stress factor."""
        # Temperature stress (optimal range 15-25°C)
        temp_stress = max(0, abs(self.temperature - 20) / 20)
        
        # Wind stress (high wind reduces pollinator activity)
        wind_stress = min(1.0, self.wind_speed / 10.0)
        
        # Rain stress (rain prevents pollinator activity)
        rain_stress = min(1.0, self.rainfall / 5.0)
        
        # Combined stress (weighted average)
        total_stress = (temp_stress * 0.3 + wind_stress * 0.3 + 
                       rain_stress * 0.2 + self.pest_pressure * 0.2)
        
        return min(1.0, total_stress)


class Node(BaseModel):
    """
    Represents a flower cluster or crop section in the pollination network.
    
    Each node has spatial coordinates, crop characteristics, and environmental
    conditions that affect pollination probability and yield potential.
    """
    
    id: str
    x: float = PydanticField(..., description="X coordinate in field")
    y: float = PydanticField(..., description="Y coordinate in field")
    crop_type: CropType
    flowering_stage: FloweringStage
    crop_importance: float = PydanticField(..., ge=0, le=5, description="Yield importance (1-5 scale)")
    flower_density: float = PydanticField(..., ge=0, le=1, description="Density of flowers (0-1)")
    environmental_conditions: EnvironmentalConditions
    base_pollination_probability: float = PydanticField(default=0.5, ge=0, le=1)
    
    @validator('crop_importance')
    def validate_crop_importance(cls, v):
        if not 1 <= v <= 5:
            raise ValueError('Crop importance must be between 1 and 5')
        return v
    
    def get_effective_pollination_probability(self) -> float:
        """Calculate effective pollination probability considering all factors."""
        base_prob = self.base_pollination_probability
        
        # Flowering stage multiplier
        stage_multipliers = {
            FloweringStage.PRE_BLOOM: 0.1,
            FloweringStage.EARLY_BLOOM: 0.6,
            FloweringStage.PEAK_BLOOM: 1.0,
            FloweringStage.LATE_BLOOM: 0.7,
            FloweringStage.POST_BLOOM: 0.2
        }
        
        stage_factor = stage_multipliers[self.flowering_stage]
        density_factor = self.flower_density
        stress_factor = 1.0 - self.environmental_conditions.get_stress_factor()
        
        return base_prob * stage_factor * density_factor * stress_factor
    
    def get_yield_potential(self) -> float:
        """Calculate potential yield based on pollination and crop importance."""
        return self.get_effective_pollination_probability() * self.crop_importance


class Edge(BaseModel):
    """
    Represents pollinator movement probability between two nodes.
    
    Edges encode the likelihood that a pollinator will move from one flower
    cluster to another, considering distance and environmental factors.
    """
    
    source_id: str
    target_id: str
    distance: float = PydanticField(..., ge=0, description="Physical distance between nodes")
    movement_probability: float = PydanticField(..., ge=0, le=1, description="Base movement probability")
    
    def get_effective_movement_probability(self, source_node: Node, target_node: Node) -> float:
        """Calculate effective movement probability considering environmental factors."""
        base_prob = self.movement_probability
        
        # Distance decay (closer nodes have higher movement probability)
        distance_factor = np.exp(-self.distance / 100.0)  # 100m characteristic distance
        
        # Environmental compatibility (similar conditions favor movement)
        source_stress = source_node.environmental_conditions.get_stress_factor()
        target_stress = target_node.environmental_conditions.get_stress_factor()
        stress_compatibility = 1.0 - abs(source_stress - target_stress)
        
        return base_prob * distance_factor * stress_compatibility


class Field(BaseModel):
    """
    Represents a complete agricultural field with nodes and edges.
    
    The field contains all flower clusters (nodes) and their interconnections
    (edges), along with methods for optimization and analysis.
    """
    
    name: str
    nodes: Dict[str, Node] = PydanticField(default_factory=dict)
    edges: List[Edge] = PydanticField(default_factory=list)
    field_width: float = PydanticField(..., gt=0, description="Field width in meters")
    field_height: float = PydanticField(..., gt=0, description="Field height in meters")
    
    def add_node(self, node: Node) -> None:
        """Add a node to the field."""
        self.nodes[node.id] = node
    
    def add_edge(self, edge: Edge) -> None:
        """Add an edge to the field."""
        if edge.source_id not in self.nodes or edge.target_id not in self.nodes:
            raise ValueError("Edge references non-existent nodes")
        self.edges.append(edge)
    
    def get_adjacency_matrix(self) -> np.ndarray:
        """Get adjacency matrix for the field network."""
        node_ids = list(self.nodes.keys())
        n = len(node_ids)
        matrix = np.zeros((n, n))
        
        id_to_index = {node_id: i for i, node_id in enumerate(node_ids)}
        
        for edge in self.edges:
            i = id_to_index[edge.source_id]
            j = id_to_index[edge.target_id]
            source_node = self.nodes[edge.source_id]
            target_node = self.nodes[edge.target_id]
            matrix[i][j] = edge.get_effective_movement_probability(source_node, target_node)
        
        return matrix
    
    def get_node_weights(self) -> np.ndarray:
        """Get weight vector for nodes based on yield potential."""
        return np.array([node.get_yield_potential() for node in self.nodes.values()])
    
    def get_stressed_nodes(self, threshold: float = 0.7) -> List[str]:
        """Identify nodes with high environmental stress."""
        stressed = []
        for node_id, node in self.nodes.items():
            if node.environmental_conditions.get_stress_factor() > threshold:
                stressed.append(node_id)
        return stressed
    
    def calculate_total_yield_potential(self) -> float:
        """Calculate total potential yield for the field."""
        return sum(node.get_yield_potential() for node in self.nodes.values())
    
    def get_field_statistics(self) -> Dict:
        """Get comprehensive field statistics."""
        nodes_by_stage = {}
        for stage in FloweringStage:
            nodes_by_stage[stage.value] = len([
                n for n in self.nodes.values() 
                if n.flowering_stage == stage
            ])
        
        avg_stress = np.mean([
            node.environmental_conditions.get_stress_factor() 
            for node in self.nodes.values()
        ])
        
        return {
            "total_nodes": len(self.nodes),
            "total_edges": len(self.edges),
            "nodes_by_flowering_stage": nodes_by_stage,
            "average_stress_factor": avg_stress,
            "total_yield_potential": self.calculate_total_yield_potential(),
            "field_area": self.field_width * self.field_height
        }


@dataclass
class OptimizationResult:
    """Results from quantum optimization."""
    
    field: Field
    optimal_solution: np.ndarray  # Binary array indicating pollination schedule
    objective_value: float        # Achieved objective function value
    iterations: int              # Number of adaptive feedback iterations
    computation_time: float      # Time taken for optimization
    quantum_energy: float        # Energy of quantum solution
    stress_adjustments: Dict[str, float]  # Adjustments made for stressed nodes
    
    def get_pollinated_nodes(self) -> List[str]:
        """Get list of node IDs that should be pollinated."""
        node_ids = list(self.field.nodes.keys())
        return [node_ids[i] for i, val in enumerate(self.optimal_solution) if val == 1]
    
    def get_coverage_percentage(self) -> float:
        """Calculate percentage of nodes covered by pollination."""
        return (np.sum(self.optimal_solution) / len(self.optimal_solution)) * 100
    
    def get_yield_improvement(self) -> float:
        """Calculate yield improvement compared to random pollination."""
        # Calculate yield with optimal solution
        optimal_yield = 0
        node_ids = list(self.field.nodes.keys())
        for i, pollinated in enumerate(self.optimal_solution):
            if pollinated:
                node = self.field.nodes[node_ids[i]]
                optimal_yield += node.get_yield_potential()
        
        # Calculate average random yield (50% coverage assumption)
        random_yield = self.field.calculate_total_yield_potential() * 0.5
        
        return ((optimal_yield - random_yield) / random_yield) * 100 if random_yield > 0 else 0
