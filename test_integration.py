"""
Integration test script for QEPO system.

This script tests the complete pipeline from field generation to optimization
and visualization to ensure all components work together correctly.
"""

import sys
import os
import traceback
import numpy as np

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from qepo.core.models import Node, Edge, Field, EnvironmentalConditions, FloweringStage, CropType
        print("✅ Core models imported successfully")
    except Exception as e:
        print(f"❌ Failed to import core models: {e}")
        return False
    
    try:
        from qepo.data.synthetic_generator import generate_field, SyntheticFieldGenerator
        print("✅ Synthetic data generator imported successfully")
    except Exception as e:
        print(f"❌ Failed to import synthetic generator: {e}")
        return False
    
    try:
        from qepo.core.qubo_formulation import QUBOFormulator
        print("✅ QUBO formulation imported successfully")
    except Exception as e:
        print(f"❌ Failed to import QUBO formulation: {e}")
        return False
    
    try:
        from qepo.core.quantum_solver import QuantumSolver, optimize_pollination, SolverConfig
        print("✅ Quantum solver imported successfully")
    except Exception as e:
        print(f"❌ Failed to import quantum solver: {e}")
        return False
    
    try:
        from qepo.core.adaptive_feedback import AdaptiveFeedbackEngine, AdaptiveConfig
        print("✅ Adaptive feedback imported successfully")
    except Exception as e:
        print(f"❌ Failed to import adaptive feedback: {e}")
        return False
    
    return True


def test_field_generation():
    """Test synthetic field generation."""
    print("\nTesting field generation...")
    
    try:
        from qepo.data.synthetic_generator import generate_field
        
        field = generate_field(grid_size=5, seed=42)
        
        assert len(field.nodes) == 25, f"Expected 25 nodes, got {len(field.nodes)}"
        assert len(field.edges) > 0, "No edges generated"
        
        print(f"✅ Generated field with {len(field.nodes)} nodes and {len(field.edges)} edges")
        
        # Test field statistics
        stats = field.get_field_statistics()
        print(f"   Total yield potential: {stats['total_yield_potential']:.2f}")
        print(f"   Average stress factor: {stats['average_stress_factor']:.2f}")
        
        return field
        
    except Exception as e:
        print(f"❌ Field generation failed: {e}")
        traceback.print_exc()
        return None


def test_qubo_formulation(field):
    """Test QUBO formulation."""
    print("\nTesting QUBO formulation...")
    
    try:
        from qepo.core.qubo_formulation import QUBOFormulator
        
        formulator = QUBOFormulator(field)
        Q, params = formulator.formulate_qubo()
        
        expected_size = len(field.nodes)
        assert Q.shape == (expected_size, expected_size), f"Wrong QUBO matrix shape: {Q.shape}"
        
        print(f"✅ Generated QUBO matrix of size {Q.shape}")
        print(f"   Matrix density: {np.count_nonzero(Q) / Q.size * 100:.1f}%")
        
        return Q, formulator
        
    except Exception as e:
        print(f"❌ QUBO formulation failed: {e}")
        traceback.print_exc()
        return None, None


def test_optimization(field):
    """Test basic optimization."""
    print("\nTesting optimization...")
    
    try:
        from qepo.core.quantum_solver import optimize_pollination, SolverConfig
        
        solver_config = SolverConfig(
            solver_type="classical",  # Use classical for reliability
            max_iterations=50
        )
        
        result = optimize_pollination(field, solver_config)
        
        assert result.optimal_solution is not None, "No solution returned"
        assert len(result.optimal_solution) == len(field.nodes), "Solution size mismatch"
        
        print(f"✅ Optimization completed successfully")
        print(f"   Coverage: {result.get_coverage_percentage():.1f}%")
        print(f"   Yield improvement: {result.get_yield_improvement():.1f}%")
        print(f"   Computation time: {result.computation_time:.3f}s")
        
        return result
        
    except Exception as e:
        print(f"❌ Optimization failed: {e}")
        traceback.print_exc()
        return None


def test_adaptive_feedback(field):
    """Test adaptive feedback mechanism."""
    print("\nTesting adaptive feedback...")
    
    try:
        from qepo.core.adaptive_feedback import AdaptiveFeedbackEngine, AdaptiveConfig
        from qepo.core.quantum_solver import SolverConfig
        
        adaptive_config = AdaptiveConfig(max_iterations=2)
        solver_config = SolverConfig(solver_type="classical")
        
        adaptive_engine = AdaptiveFeedbackEngine(adaptive_config)
        result = adaptive_engine.adaptive_optimize(field, solver_config)
        
        assert result.iterations >= 1, "No iterations completed"
        
        print(f"✅ Adaptive optimization completed")
        print(f"   Iterations: {result.iterations}")
        print(f"   Final coverage: {result.get_coverage_percentage():.1f}%")
        
        summary = adaptive_engine.get_adaptation_summary()
        if summary:
            print(f"   Improvement: {summary.get('improvement_percentage', 0):.1f}%")
        
        return result
        
    except Exception as e:
        print(f"❌ Adaptive feedback failed: {e}")
        traceback.print_exc()
        return None


def main():
    """Run complete integration test."""
    print("🌸 QEPO Integration Test")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Cannot continue.")
        return False
    
    # Test field generation
    field = test_field_generation()
    if field is None:
        print("\n❌ Field generation failed. Cannot continue.")
        return False
    
    # Test QUBO formulation
    Q, formulator = test_qubo_formulation(field)
    if Q is None:
        print("\n❌ QUBO formulation failed. Cannot continue.")
        return False
    
    # Test basic optimization
    result = test_optimization(field)
    if result is None:
        print("\n❌ Basic optimization failed. Cannot continue.")
        return False
    
    # Test adaptive feedback
    adaptive_result = test_adaptive_feedback(field)
    if adaptive_result is None:
        print("\n❌ Adaptive feedback failed.")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All integration tests passed!")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Run the dashboard: streamlit run qepo/visualization/dashboard.py")
    print("3. Run the example: python examples/basic_usage.py")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
