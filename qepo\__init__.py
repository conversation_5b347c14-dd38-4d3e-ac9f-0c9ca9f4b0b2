"""
Quantum-Enhanced Pollination Optimization (QEPO)

A quantum-inspired optimization system for maximizing crop pollination efficiency
using bio-inspired adaptive feedback mechanisms.
"""

__version__ = "0.1.0"
__author__ = "QEPO Development Team"

from .core.models import Node, Edge, Field
from .core.quantum_solver import optimize_pollination
from .data.synthetic_generator import generate_field

__all__ = [
    "Node",
    "Edge", 
    "Field",
    "optimize_pollination",
    "generate_field"
]
