"""
Synthetic data generator for QEPO system.

This module generates realistic synthetic agricultural field data for testing
and demonstration of the quantum pollination optimization algorithm.
"""

import numpy as np
import random
from typing import Dict, List, Optional, Tuple
from ..core.models import (
    Node, Edge, Field, EnvironmentalConditions, 
    FloweringStage, CropType, OptimizationResult
)


class SyntheticFieldGenerator:
    """Generator for synthetic agricultural field data."""
    
    def __init__(self, seed: Optional[int] = None):
        """Initialize generator with optional random seed."""
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)
    
    def generate_environmental_conditions(
        self, 
        base_temp: float = 20.0,
        temp_variance: float = 5.0,
        stress_probability: float = 0.2
    ) -> EnvironmentalConditions:
        """Generate realistic environmental conditions."""
        
        temperature = np.random.normal(base_temp, temp_variance)
        humidity = np.random.uniform(40, 80)
        wind_speed = np.random.exponential(3.0)  # Most days low wind
        rainfall = np.random.exponential(1.0) if np.random.random() < 0.3 else 0
        
        # Pest pressure - occasional high stress
        if np.random.random() < stress_probability:
            pest_pressure = np.random.uniform(0.6, 1.0)
        else:
            pest_pressure = np.random.uniform(0.0, 0.3)
        
        return EnvironmentalConditions(
            temperature=temperature,
            humidity=humidity,
            wind_speed=wind_speed,
            rainfall=rainfall,
            pest_pressure=pest_pressure
        )
    
    def generate_node(
        self, 
        node_id: str, 
        x: float, 
        y: float,
        crop_type: Optional[CropType] = None,
        flowering_stage: Optional[FloweringStage] = None
    ) -> Node:
        """Generate a single node with realistic parameters."""
        
        if crop_type is None:
            crop_type = np.random.choice(list(CropType))
        
        if flowering_stage is None:
            # Weight towards peak bloom for realistic scenarios
            stage_weights = [0.1, 0.2, 0.4, 0.2, 0.1]
            flowering_stage = np.random.choice(list(FloweringStage), p=stage_weights)
        
        crop_importance = np.random.uniform(1.5, 4.5)
        flower_density = np.random.beta(2, 2)  # Bell curve around 0.5
        base_pollination_prob = np.random.beta(3, 2)  # Slightly higher probability
        
        environmental_conditions = self.generate_environmental_conditions()
        
        return Node(
            id=node_id,
            x=x,
            y=y,
            crop_type=crop_type,
            flowering_stage=flowering_stage,
            crop_importance=crop_importance,
            flower_density=flower_density,
            environmental_conditions=environmental_conditions,
            base_pollination_probability=base_pollination_prob
        )
    
    def calculate_distance(self, x1: float, y1: float, x2: float, y2: float) -> float:
        """Calculate Euclidean distance between two points."""
        return np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    
    def generate_edges(self, nodes: Dict[str, Node], max_distance: float = 200.0) -> List[Edge]:
        """Generate edges between nodes within maximum distance."""
        edges = []
        node_list = list(nodes.values())
        
        for i, source in enumerate(node_list):
            for j, target in enumerate(node_list):
                if i != j:  # No self-loops
                    distance = self.calculate_distance(source.x, source.y, target.x, target.y)
                    
                    if distance <= max_distance:
                        # Movement probability decreases with distance
                        base_prob = np.exp(-distance / 50.0)  # 50m characteristic distance
                        
                        # Add some randomness
                        movement_prob = base_prob * np.random.uniform(0.7, 1.3)
                        movement_prob = min(1.0, max(0.0, movement_prob))
                        
                        edge = Edge(
                            source_id=source.id,
                            target_id=target.id,
                            distance=distance,
                            movement_probability=movement_prob
                        )
                        edges.append(edge)
        
        return edges


def generate_field(
    grid_size: int = 10,
    field_width: float = 1000.0,
    field_height: float = 1000.0,
    crop_diversity: bool = True,
    stress_zones: bool = True,
    seed: Optional[int] = None
) -> Field:
    """
    Generate a synthetic agricultural field.
    
    Args:
        grid_size: Number of nodes per side (creates grid_size x grid_size grid)
        field_width: Width of field in meters
        field_height: Height of field in meters
        crop_diversity: Whether to include multiple crop types
        stress_zones: Whether to include high-stress environmental zones
        seed: Random seed for reproducibility
    
    Returns:
        Field object with generated nodes and edges
    """
    
    generator = SyntheticFieldGenerator(seed)
    
    # Create field
    field = Field(
        name=f"Synthetic Field {grid_size}x{grid_size}",
        field_width=field_width,
        field_height=field_height
    )
    
    # Generate grid of nodes
    x_spacing = field_width / (grid_size - 1) if grid_size > 1 else 0
    y_spacing = field_height / (grid_size - 1) if grid_size > 1 else 0
    
    crop_types = list(CropType) if crop_diversity else [CropType.APPLE]
    
    for i in range(grid_size):
        for j in range(grid_size):
            node_id = f"node_{i}_{j}"
            x = i * x_spacing
            y = j * y_spacing
            
            # Select crop type
            crop_type = np.random.choice(crop_types)
            
            # Create stress zones in corners if enabled
            if stress_zones and (i < 2 or i >= grid_size-2 or j < 2 or j >= grid_size-2):
                # Force high stress in corner regions
                node = generator.generate_node(node_id, x, y, crop_type)
                node.environmental_conditions.pest_pressure = np.random.uniform(0.7, 1.0)
            else:
                node = generator.generate_node(node_id, x, y, crop_type)
            
            field.add_node(node)
    
    # Generate edges
    edges = generator.generate_edges(field.nodes)
    for edge in edges:
        field.add_edge(edge)
    
    return field


def generate_scenario_variations(base_field: Field, num_scenarios: int = 5) -> List[Field]:
    """
    Generate multiple scenario variations of a base field.
    
    Useful for testing optimization under different environmental conditions.
    """
    scenarios = []
    generator = SyntheticFieldGenerator()
    
    for i in range(num_scenarios):
        # Create a copy of the base field
        scenario_field = Field(
            name=f"{base_field.name}_scenario_{i+1}",
            field_width=base_field.field_width,
            field_height=base_field.field_height
        )
        
        # Copy nodes with modified environmental conditions
        for node_id, original_node in base_field.nodes.items():
            new_conditions = generator.generate_environmental_conditions()
            
            new_node = Node(
                id=original_node.id,
                x=original_node.x,
                y=original_node.y,
                crop_type=original_node.crop_type,
                flowering_stage=original_node.flowering_stage,
                crop_importance=original_node.crop_importance,
                flower_density=original_node.flower_density,
                environmental_conditions=new_conditions,
                base_pollination_probability=original_node.base_pollination_probability
            )
            scenario_field.add_node(new_node)
        
        # Copy edges (they'll recalculate probabilities based on new conditions)
        for edge in base_field.edges:
            scenario_field.add_edge(edge)
        
        scenarios.append(scenario_field)
    
    return scenarios
