"""
Simplified Streamlit dashboard for QEPO system.

This version provides core functionality without complex plotting dependencies.
"""

import streamlit as st
import numpy as np
import pandas as pd
import time
from typing import Dict, Optional

# Import QEPO components
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from qepo.data.synthetic_generator import generate_field, generate_scenario_variations
from qepo.core.quantum_solver import optimize_pollination, SolverConfig
from qepo.core.adaptive_feedback import AdaptiveFeedbackEngine, AdaptiveConfig
from qepo.core.models import CropType, FloweringStage


def display_field_summary(field):
    """Display field summary information."""
    stats = field.get_field_statistics()
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Nodes", stats['total_nodes'])
    with col2:
        st.metric("Total Connections", stats['total_edges'])
    with col3:
        st.metric("Avg Stress Factor", f"{stats['average_stress_factor']:.2f}")
    with col4:
        st.metric("Yield Potential", f"{stats['total_yield_potential']:.1f}")
    
    # Flowering stage distribution
    st.subheader("Flowering Stage Distribution")
    stage_data = []
    for stage, count in stats['nodes_by_flowering_stage'].items():
        stage_data.append({"Stage": stage.replace('_', ' ').title(), "Count": count})
    
    df_stages = pd.DataFrame(stage_data)
    st.dataframe(df_stages, use_container_width=True)


def display_optimization_results(result, field):
    """Display optimization results."""
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Coverage", f"{result.get_coverage_percentage():.1f}%")
    with col2:
        st.metric("Yield Improvement", f"{result.get_yield_improvement():.1f}%")
    with col3:
        st.metric("Computation Time", f"{result.computation_time:.3f}s")
    with col4:
        st.metric("Iterations", result.iterations)
    
    # Selected nodes analysis
    pollinated_nodes = result.get_pollinated_nodes()
    
    st.subheader("Selected Nodes Analysis")
    
    # Create dataframe of selected nodes
    selected_data = []
    for node_id in pollinated_nodes:
        node = field.nodes[node_id]
        selected_data.append({
            "Node ID": node_id,
            "Crop Type": node.crop_type.value,
            "Flowering Stage": node.flowering_stage.value,
            "Yield Potential": f"{node.get_yield_potential():.2f}",
            "Stress Factor": f"{node.environmental_conditions.get_stress_factor():.2f}",
            "X Position": f"{node.x:.0f}m",
            "Y Position": f"{node.y:.0f}m"
        })
    
    if selected_data:
        df_selected = pd.DataFrame(selected_data)
        st.dataframe(df_selected, use_container_width=True)
    else:
        st.warning("No nodes were selected for pollination.")
    
    # Stress adjustments (if available)
    if result.stress_adjustments:
        st.subheader("Stress Adjustments")
        adj_data = []
        for node_id, adjustment in result.stress_adjustments.items():
            adj_data.append({
                "Node ID": node_id,
                "Adjustment": f"{adjustment:.3f}",
                "Type": "Increase" if adjustment > 0 else "Decrease"
            })
        
        df_adjustments = pd.DataFrame(adj_data)
        st.dataframe(df_adjustments, use_container_width=True)


def main():
    """Main dashboard application."""
    
    st.set_page_config(
        page_title="QEPO - Quantum-Enhanced Pollination Optimization",
        page_icon="🌸",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🌸 Quantum-Enhanced Pollination Optimization (QEPO)")
    st.markdown("*Optimizing crop pollination using quantum computing and bio-inspired algorithms*")
    
    # Sidebar controls
    st.sidebar.header("🎛️ Configuration")
    
    # Field generation parameters
    st.sidebar.subheader("Field Parameters")
    grid_size = st.sidebar.slider("Grid Size", min_value=4, max_value=12, value=8)
    field_width = st.sidebar.number_input("Field Width (m)", min_value=400, max_value=1200, value=800)
    field_height = st.sidebar.number_input("Field Height (m)", min_value=400, max_value=1200, value=800)
    crop_diversity = st.sidebar.checkbox("Multiple Crop Types", value=True)
    stress_zones = st.sidebar.checkbox("Include Stress Zones", value=True)
    random_seed = st.sidebar.number_input("Random Seed", min_value=0, max_value=9999, value=42)
    
    # Optimization parameters
    st.sidebar.subheader("Optimization Parameters")
    solver_type = st.sidebar.selectbox(
        "Solver Type", 
        ["classical", "simulated_annealing"],
        index=0
    )
    
    use_adaptive = st.sidebar.checkbox("Use Adaptive Feedback", value=True)
    max_iterations = st.sidebar.slider("Max Adaptive Iterations", min_value=1, max_value=5, value=3)
    
    # QUBO formulation parameters
    st.sidebar.subheader("QUBO Parameters")
    yield_weight = st.sidebar.slider("Yield Weight", min_value=0.1, max_value=3.0, value=1.0, step=0.1)
    movement_weight = st.sidebar.slider("Movement Weight", min_value=0.1, max_value=2.0, value=0.5, step=0.1)
    stress_penalty = st.sidebar.slider("Stress Penalty", min_value=0.5, max_value=5.0, value=2.0, step=0.1)
    
    # Initialize session state
    if 'field_generated' not in st.session_state:
        st.session_state.field_generated = False
    if 'optimization_run' not in st.session_state:
        st.session_state.optimization_run = False
    
    # Generate field section
    st.header("🌱 Field Generation")
    
    if st.button("🌱 Generate New Field", type="primary"):
        with st.spinner("Generating synthetic field..."):
            field = generate_field(
                grid_size=grid_size,
                field_width=field_width,
                field_height=field_height,
                crop_diversity=crop_diversity,
                stress_zones=stress_zones,
                seed=random_seed
            )
            st.session_state.field = field
            st.session_state.field_generated = True
            st.session_state.optimization_run = False
        
        st.success(f"Generated field with {len(field.nodes)} nodes and {len(field.edges)} connections")
    
    # Display field information
    if st.session_state.field_generated and hasattr(st.session_state, 'field'):
        field = st.session_state.field
        
        st.subheader("Field Summary")
        display_field_summary(field)
        
        # Show sample nodes
        with st.expander("Sample Nodes Details"):
            sample_nodes = list(field.nodes.items())[:10]  # Show first 10 nodes
            node_data = []
            
            for node_id, node in sample_nodes:
                node_data.append({
                    "Node ID": node_id,
                    "Position": f"({node.x:.0f}, {node.y:.0f})",
                    "Crop": node.crop_type.value,
                    "Stage": node.flowering_stage.value,
                    "Importance": f"{node.crop_importance:.1f}",
                    "Density": f"{node.flower_density:.2f}",
                    "Yield Potential": f"{node.get_yield_potential():.2f}",
                    "Stress": f"{node.environmental_conditions.get_stress_factor():.2f}"
                })
            
            df_nodes = pd.DataFrame(node_data)
            st.dataframe(df_nodes, use_container_width=True)
    
    # Optimization section
    if st.session_state.field_generated and hasattr(st.session_state, 'field'):
        st.header("🚀 Run Optimization")
        
        if st.button("🎯 Optimize Pollination", type="primary"):
            field = st.session_state.field
            
            # Configure solver
            solver_config = SolverConfig(
                solver_type=solver_type,
                num_reads=500,
                max_iterations=100
            )
            
            # Configure formulation
            formulation_params = {
                'yield_weight': yield_weight,
                'movement_weight': movement_weight,
                'stress_penalty_weight': stress_penalty,
                'coverage_constraint': True,
                'flowering_bonus': True,
                'spatial_clustering': True
            }
            
            with st.spinner("Running quantum optimization..."):
                if use_adaptive:
                    # Use adaptive feedback
                    adaptive_config = AdaptiveConfig(max_iterations=max_iterations)
                    adaptive_engine = AdaptiveFeedbackEngine(adaptive_config)
                    
                    result = adaptive_engine.adaptive_optimize(
                        field, solver_config, formulation_params
                    )
                    
                    st.session_state.adaptation_summary = adaptive_engine.get_adaptation_summary()
                    st.session_state.adaptation_history = adaptive_engine.iteration_history
                else:
                    # Standard optimization
                    result = optimize_pollination(field, solver_config, formulation_params)
                    st.session_state.adaptation_summary = None
                    st.session_state.adaptation_history = []
                
                st.session_state.optimization_result = result
                st.session_state.optimization_run = True
            
            st.success("Optimization completed!")
    
    # Results section
    if st.session_state.optimization_run and hasattr(st.session_state, 'optimization_result'):
        result = st.session_state.optimization_result
        field = st.session_state.field
        
        st.header("📊 Optimization Results")
        
        display_optimization_results(result, field)
        
        # Adaptation details
        if hasattr(st.session_state, 'adaptation_summary') and st.session_state.adaptation_summary:
            st.subheader("🔄 Adaptive Feedback Summary")
            
            summary = st.session_state.adaptation_summary
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Iterations", summary.get('total_iterations', 0))
            with col2:
                st.metric("Final Improvement", f"{summary.get('improvement_percentage', 0):.1f}%")
            with col3:
                st.metric("Memory Size", summary.get('memory_size', 0))
            
            # Iteration history
            if st.session_state.adaptation_history:
                st.write("**Iteration History:**")
                history_data = []
                for info in st.session_state.adaptation_history:
                    history_data.append({
                        "Iteration": info['iteration'],
                        "Objective": f"{info['objective']:.3f}",
                        "Stressed Nodes": info['stressed_nodes'],
                        "Under-covered Nodes": info['under_covered_nodes'],
                        "Total Adjustments": info['total_adjustments']
                    })
                
                df_history = pd.DataFrame(history_data)
                st.dataframe(df_history, use_container_width=True)
        
        # Comparison with baseline
        st.subheader("📈 Performance Comparison")
        
        baseline_yield = field.calculate_total_yield_potential() * 0.5  # 50% random coverage
        optimized_yield = sum(
            field.nodes[node_id].get_yield_potential() 
            for node_id in result.get_pollinated_nodes()
        )
        
        comparison_data = [
            {"Method": "Random Baseline", "Yield": f"{baseline_yield:.2f}", "Coverage": "50.0%"},
            {"Method": "QEPO Optimized", "Yield": f"{optimized_yield:.2f}", 
             "Coverage": f"{result.get_coverage_percentage():.1f}%"}
        ]
        
        df_comparison = pd.DataFrame(comparison_data)
        st.dataframe(df_comparison, use_container_width=True)
        
        improvement = ((optimized_yield - baseline_yield) / baseline_yield) * 100
        st.success(f"🎉 QEPO achieved {improvement:.1f}% yield improvement over random pollination!")
    
    # Information section
    st.sidebar.markdown("---")
    st.sidebar.subheader("ℹ️ About QEPO")
    st.sidebar.markdown("""
    **Quantum-Enhanced Pollination Optimization** combines:
    
    - 🔬 **Quantum Computing**: QUBO formulation with quantum annealing
    - 🧬 **Bio-inspired AI**: Immune system adaptive feedback
    - 🌾 **Agricultural Science**: Realistic pollination modeling
    - 📊 **Data Visualization**: Interactive field analysis
    
    **Use Cases:**
    - Precision agriculture planning
    - Crop yield optimization
    - Environmental impact assessment
    - Research and education
    """)
    
    # Instructions
    if not st.session_state.field_generated:
        st.info("👆 Start by generating a synthetic field using the sidebar controls.")
    elif not st.session_state.optimization_run:
        st.info("👆 Now run the optimization to see quantum-enhanced results!")


if __name__ == "__main__":
    main()
