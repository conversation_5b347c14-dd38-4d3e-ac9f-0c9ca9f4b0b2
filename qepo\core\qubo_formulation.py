"""
QUBO (Quadratic Unconstrained Binary Optimization) formulation for pollination optimization.

This module converts the pollination optimization problem into a QUBO matrix
that can be solved using quantum annealing or classical optimization methods.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from ..core.models import Field, Node, Edge


class QUBOFormulator:
    """
    Formulates the pollination optimization problem as a QUBO.
    
    The QUBO formulation maximizes pollination coverage while considering:
    - Node yield potential (linear terms)
    - Pollinator movement efficiency (quadratic terms)
    - Environmental constraints (penalty terms)
    """
    
    def __init__(self, field: Field):
        """Initialize with a field to optimize."""
        self.field = field
        self.node_ids = list(field.nodes.keys())
        self.n_nodes = len(self.node_ids)
        self.node_to_index = {node_id: i for i, node_id in enumerate(self.node_ids)}
    
    def create_objective_matrix(
        self, 
        yield_weight: float = 1.0,
        movement_weight: float = 0.5,
        stress_penalty_weight: float = 2.0
    ) -> np.ndarray:
        """
        Create the QUBO matrix for the optimization problem.
        
        Args:
            yield_weight: Weight for yield potential terms
            movement_weight: Weight for pollinator movement efficiency
            stress_penalty_weight: Penalty weight for high-stress nodes
        
        Returns:
            QUBO matrix Q where objective = x^T Q x (to be maximized)
        """
        Q = np.zeros((self.n_nodes, self.n_nodes))
        
        # Linear terms (diagonal): yield potential of each node
        for i, node_id in enumerate(self.node_ids):
            node = self.field.nodes[node_id]
            yield_potential = node.get_yield_potential()
            
            # Apply stress penalty
            stress_factor = node.environmental_conditions.get_stress_factor()
            stress_penalty = stress_penalty_weight * stress_factor
            
            # Diagonal term (linear coefficient in QUBO)
            Q[i, i] = yield_weight * yield_potential - stress_penalty
        
        # Quadratic terms (off-diagonal): pollinator movement synergies
        for edge in self.field.edges:
            i = self.node_to_index[edge.source_id]
            j = self.node_to_index[edge.target_id]
            
            source_node = self.field.nodes[edge.source_id]
            target_node = self.field.nodes[edge.target_id]
            
            # Movement probability between nodes
            movement_prob = edge.get_effective_movement_probability(source_node, target_node)
            
            # Synergy bonus for pollinating connected nodes
            synergy_bonus = movement_weight * movement_prob
            
            # Add to both Q[i,j] and Q[j,i] for symmetry
            Q[i, j] += synergy_bonus / 2
            Q[j, i] += synergy_bonus / 2
        
        return Q
    
    def add_coverage_constraint(
        self, 
        Q: np.ndarray, 
        min_coverage: float = 0.3,
        max_coverage: float = 0.8,
        penalty_weight: float = 10.0
    ) -> np.ndarray:
        """
        Add coverage constraints to the QUBO matrix.
        
        Ensures that a reasonable percentage of nodes are selected for pollination.
        """
        n = self.n_nodes
        target_coverage = (min_coverage + max_coverage) / 2
        target_nodes = int(target_coverage * n)
        
        # Penalty for deviating from target coverage
        # Add quadratic penalty: penalty_weight * (sum(x_i) - target)^2
        
        # Expand: penalty_weight * (sum(x_i)^2 - 2*target*sum(x_i) + target^2)
        # The constant term target^2 doesn't affect optimization
        
        # Linear terms: -2 * penalty_weight * target
        for i in range(n):
            Q[i, i] -= 2 * penalty_weight * target_nodes
        
        # Quadratic terms: penalty_weight (for all pairs)
        for i in range(n):
            for j in range(n):
                if i != j:
                    Q[i, j] += penalty_weight
        
        return Q
    
    def add_flowering_stage_bonus(
        self, 
        Q: np.ndarray, 
        bonus_weight: float = 1.5
    ) -> np.ndarray:
        """
        Add bonus for nodes in peak flowering stage.
        """
        from ..core.models import FloweringStage
        
        for i, node_id in enumerate(self.node_ids):
            node = self.field.nodes[node_id]
            if node.flowering_stage == FloweringStage.PEAK_BLOOM:
                Q[i, i] += bonus_weight
        
        return Q
    
    def add_spatial_clustering_bonus(
        self, 
        Q: np.ndarray, 
        clustering_weight: float = 0.3,
        max_distance: float = 100.0
    ) -> np.ndarray:
        """
        Add bonus for spatially clustered pollination (more efficient for pollinators).
        """
        for i, node_id_i in enumerate(self.node_ids):
            node_i = self.field.nodes[node_id_i]
            
            for j, node_id_j in enumerate(self.node_ids):
                if i != j:
                    node_j = self.field.nodes[node_id_j]
                    
                    # Calculate distance
                    distance = np.sqrt((node_i.x - node_j.x)**2 + (node_i.y - node_j.y)**2)
                    
                    if distance <= max_distance:
                        # Closer nodes get higher clustering bonus
                        proximity_factor = 1.0 - (distance / max_distance)
                        clustering_bonus = clustering_weight * proximity_factor
                        
                        Q[i, j] += clustering_bonus / 2
                        Q[j, i] += clustering_bonus / 2
        
        return Q
    
    def formulate_qubo(
        self,
        yield_weight: float = 1.0,
        movement_weight: float = 0.5,
        stress_penalty_weight: float = 2.0,
        coverage_constraint: bool = True,
        min_coverage: float = 0.3,
        max_coverage: float = 0.8,
        coverage_penalty_weight: float = 10.0,
        flowering_bonus: bool = True,
        flowering_bonus_weight: float = 1.5,
        spatial_clustering: bool = True,
        clustering_weight: float = 0.3
    ) -> Tuple[np.ndarray, Dict]:
        """
        Create complete QUBO formulation for the pollination optimization problem.
        
        Returns:
            Tuple of (QUBO matrix, formulation parameters)
        """
        
        # Start with basic objective matrix
        Q = self.create_objective_matrix(
            yield_weight=yield_weight,
            movement_weight=movement_weight,
            stress_penalty_weight=stress_penalty_weight
        )
        
        # Add constraints and bonuses
        if coverage_constraint:
            Q = self.add_coverage_constraint(
                Q, min_coverage, max_coverage, coverage_penalty_weight
            )
        
        if flowering_bonus:
            Q = self.add_flowering_stage_bonus(Q, flowering_bonus_weight)
        
        if spatial_clustering:
            Q = self.add_spatial_clustering_bonus(Q, clustering_weight)
        
        # Convert to minimization problem (quantum annealers minimize)
        Q = -Q
        
        # Store formulation parameters
        params = {
            'yield_weight': yield_weight,
            'movement_weight': movement_weight,
            'stress_penalty_weight': stress_penalty_weight,
            'coverage_constraint': coverage_constraint,
            'min_coverage': min_coverage,
            'max_coverage': max_coverage,
            'coverage_penalty_weight': coverage_penalty_weight,
            'flowering_bonus': flowering_bonus,
            'flowering_bonus_weight': flowering_bonus_weight,
            'spatial_clustering': spatial_clustering,
            'clustering_weight': clustering_weight,
            'n_nodes': self.n_nodes,
            'node_ids': self.node_ids.copy()
        }
        
        return Q, params
    
    def evaluate_solution(self, solution: np.ndarray, Q: np.ndarray) -> Dict:
        """
        Evaluate a binary solution vector against the QUBO matrix.
        
        Args:
            solution: Binary array indicating which nodes to pollinate
            Q: QUBO matrix (in minimization form)
        
        Returns:
            Dictionary with evaluation metrics
        """
        if len(solution) != self.n_nodes:
            raise ValueError(f"Solution length {len(solution)} doesn't match nodes {self.n_nodes}")
        
        # QUBO objective value (remember Q is for minimization)
        objective_value = solution.T @ Q @ solution
        
        # Calculate coverage
        coverage = np.sum(solution) / self.n_nodes
        
        # Calculate total yield
        total_yield = 0
        for i, pollinated in enumerate(solution):
            if pollinated:
                node = self.field.nodes[self.node_ids[i]]
                total_yield += node.get_yield_potential()
        
        # Calculate stress exposure
        stress_exposure = 0
        for i, pollinated in enumerate(solution):
            if pollinated:
                node = self.field.nodes[self.node_ids[i]]
                stress_exposure += node.environmental_conditions.get_stress_factor()
        
        avg_stress = stress_exposure / max(1, np.sum(solution))
        
        return {
            'objective_value': objective_value,
            'coverage_percentage': coverage * 100,
            'total_yield': total_yield,
            'average_stress': avg_stress,
            'nodes_selected': int(np.sum(solution)),
            'pollinated_nodes': [self.node_ids[i] for i, val in enumerate(solution) if val == 1]
        }
    
    def get_problem_statistics(self) -> Dict:
        """Get statistics about the optimization problem."""
        total_yield_potential = self.field.calculate_total_yield_potential()
        stressed_nodes = self.field.get_stressed_nodes()
        field_stats = self.field.get_field_statistics()
        
        return {
            'problem_size': self.n_nodes,
            'total_edges': len(self.field.edges),
            'total_yield_potential': total_yield_potential,
            'stressed_nodes_count': len(stressed_nodes),
            'stressed_nodes_percentage': (len(stressed_nodes) / self.n_nodes) * 100,
            'field_statistics': field_stats
        }
