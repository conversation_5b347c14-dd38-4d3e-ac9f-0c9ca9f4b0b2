"""
Basic usage example for QEPO system.

This script demonstrates how to use the Quantum-Enhanced Pollination
Optimization system for a simple agricultural scenario.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from qepo.data.synthetic_generator import generate_field
from qepo.core.quantum_solver import optimize_pollination, SolverConfig
from qepo.core.adaptive_feedback import AdaptiveFeedbackEngine, AdaptiveConfig
from qepo.visualization.plotting import create_field_heatmap, create_yield_comparison_chart
import plotly.io as pio


def main():
    """Run basic QEPO example."""
    
    print("🌸 QEPO Basic Usage Example")
    print("=" * 50)
    
    # Step 1: Generate synthetic field
    print("\n1. Generating synthetic field...")
    field = generate_field(
        grid_size=8,
        field_width=800,
        field_height=800,
        crop_diversity=True,
        stress_zones=True,
        seed=42
    )
    
    print(f"   Generated field with {len(field.nodes)} nodes and {len(field.edges)} edges")
    
    # Display field statistics
    stats = field.get_field_statistics()
    print(f"   Total yield potential: {stats['total_yield_potential']:.2f}")
    print(f"   Average stress factor: {stats['average_stress_factor']:.2f}")
    
    # Step 2: Run standard optimization
    print("\n2. Running standard quantum optimization...")
    
    solver_config = SolverConfig(
        solver_type="simulated_annealing",
        num_reads=500
    )
    
    formulation_params = {
        'yield_weight': 1.0,
        'movement_weight': 0.5,
        'stress_penalty_weight': 2.0,
        'coverage_constraint': True,
        'flowering_bonus': True,
        'spatial_clustering': True
    }
    
    standard_result = optimize_pollination(field, solver_config, formulation_params)
    
    print(f"   Coverage: {standard_result.get_coverage_percentage():.1f}%")
    print(f"   Yield improvement: {standard_result.get_yield_improvement():.1f}%")
    print(f"   Computation time: {standard_result.computation_time:.2f}s")
    
    # Step 3: Run adaptive optimization
    print("\n3. Running adaptive optimization with bio-inspired feedback...")
    
    adaptive_config = AdaptiveConfig(
        max_iterations=3,
        stress_threshold=0.6,
        adaptation_rate=0.5
    )
    
    adaptive_engine = AdaptiveFeedbackEngine(adaptive_config)
    adaptive_result = adaptive_engine.adaptive_optimize(field, solver_config, formulation_params)
    
    print(f"   Adaptive coverage: {adaptive_result.get_coverage_percentage():.1f}%")
    print(f"   Adaptive yield improvement: {adaptive_result.get_yield_improvement():.1f}%")
    print(f"   Adaptive iterations: {adaptive_result.iterations}")
    print(f"   Total computation time: {adaptive_result.computation_time:.2f}s")
    
    # Step 4: Compare results
    print("\n4. Comparison Summary:")
    print(f"   Standard optimization yield improvement: {standard_result.get_yield_improvement():.1f}%")
    print(f"   Adaptive optimization yield improvement: {adaptive_result.get_yield_improvement():.1f}%")
    
    adaptive_advantage = (adaptive_result.get_yield_improvement() - 
                         standard_result.get_yield_improvement())
    print(f"   Adaptive advantage: {adaptive_advantage:.1f} percentage points")
    
    # Step 5: Generate visualizations
    print("\n5. Generating visualizations...")
    
    # Create field heatmap with optimal solution
    fig_heatmap = create_field_heatmap(field, adaptive_result.optimal_solution, "yield_potential")
    
    # Save visualization (optional)
    try:
        pio.write_html(fig_heatmap, "qepo_field_optimization.html")
        print("   Saved field visualization to 'qepo_field_optimization.html'")
    except Exception as e:
        print(f"   Could not save visualization: {e}")
    
    # Step 6: Scenario analysis
    print("\n6. Running scenario analysis...")
    
    scenarios = generate_scenario_variations(field, num_scenarios=3)
    scenario_results = []
    
    for i, scenario_field in enumerate(scenarios):
        scenario_result = optimize_pollination(scenario_field, solver_config, formulation_params)
        scenario_results.append((f"Scenario {i+1}", scenario_result.get_yield_improvement()))
        print(f"   Scenario {i+1} yield improvement: {scenario_result.get_yield_improvement():.1f}%")
    
    # Create yield comparison chart
    baseline_yield = field.calculate_total_yield_potential() * 0.5
    optimized_yield = sum(
        field.nodes[node_id].get_yield_potential() 
        for node_id in adaptive_result.get_pollinated_nodes()
    )
    
    fig_comparison = create_yield_comparison_chart(
        baseline_yield, 
        optimized_yield, 
        [(name, baseline_yield * (1 + improvement/100)) for name, improvement in scenario_results]
    )
    
    try:
        pio.write_html(fig_comparison, "qepo_yield_comparison.html")
        print("   Saved yield comparison to 'qepo_yield_comparison.html'")
    except Exception as e:
        print(f"   Could not save comparison chart: {e}")
    
    print("\n✅ QEPO example completed successfully!")
    print("\nTo run the interactive dashboard:")
    print("   streamlit run qepo/visualization/dashboard.py")


if __name__ == "__main__":
    main()
