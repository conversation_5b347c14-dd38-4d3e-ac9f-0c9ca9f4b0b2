"""
Quantum solver integration for QEPO system.

This module provides quantum annealing simulation and classical fallback
methods for solving the pollination optimization QUBO problem.
"""

import numpy as np
import time
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass

try:
    import dimod
    from dwave.system import D<PERSON>aveSampler, EmbeddingComposite
    from dwave.samplers import SimulatedAnnealingSampler
    DWAVE_AVAILABLE = True
except ImportError:
    DWAVE_AVAILABLE = False

try:
    from qiskit_optimization import QuadraticProgram
    from qiskit_optimization.algorithms import MinimumEigenOptimizer
    from qiskit.algorithms import QAOA
    from qiskit.primitives import Sampler
    QISKIT_AVAILABLE = True
except ImportError:
    QISKIT_AVAILABLE = False

from .models import Field, OptimizationResult
from .qubo_formulation import QUBOFormulator


@dataclass
class SolverConfig:
    """Configuration for quantum solver."""
    solver_type: str = "simulated_annealing"  # "simulated_annealing", "dwave", "qiskit", "classical"
    num_reads: int = 1000
    annealing_time: int = 20  # microseconds for D-Wave
    chain_strength: Optional[float] = None
    max_iterations: int = 100
    temperature_schedule: str = "linear"  # "linear", "exponential"


class QuantumSolver:
    """
    Quantum and classical solver for pollination optimization.
    
    Supports multiple solving approaches:
    - Simulated annealing (always available)
    - D-Wave quantum annealing (if available)
    - Qiskit QAOA (if available)
    - Classical optimization fallback
    """
    
    def __init__(self, config: Optional[SolverConfig] = None):
        """Initialize solver with configuration."""
        self.config = config or SolverConfig()
        self._check_dependencies()
    
    def _check_dependencies(self):
        """Check which quantum computing libraries are available."""
        self.available_solvers = ["classical"]
        
        if DWAVE_AVAILABLE:
            self.available_solvers.extend(["simulated_annealing", "dwave"])
        
        if QISKIT_AVAILABLE:
            self.available_solvers.append("qiskit")
        
        print(f"Available solvers: {self.available_solvers}")
    
    def solve_with_simulated_annealing(self, Q: np.ndarray) -> Tuple[np.ndarray, float, Dict]:
        """Solve using D-Wave's simulated annealing."""
        if not DWAVE_AVAILABLE:
            raise ImportError("D-Wave Ocean SDK not available")
        
        # Convert numpy matrix to D-Wave BQM
        bqm = dimod.BinaryQuadraticModel.from_numpy_matrix(Q)
        
        # Use simulated annealing sampler
        sampler = SimulatedAnnealingSampler()
        
        start_time = time.time()
        response = sampler.sample(
            bqm, 
            num_reads=self.config.num_reads,
            num_sweeps=self.config.max_iterations
        )
        computation_time = time.time() - start_time
        
        # Get best solution
        best_sample = response.first.sample
        best_energy = response.first.energy
        
        # Convert to numpy array
        node_ids = [f"x_{i}" for i in range(len(Q))]
        solution = np.array([best_sample.get(f"x_{i}", 0) for i in range(len(Q))])
        
        solver_info = {
            'solver_type': 'simulated_annealing',
            'num_reads': self.config.num_reads,
            'computation_time': computation_time,
            'energy': best_energy,
            'chain_break_fraction': 0.0  # Not applicable for SA
        }
        
        return solution, best_energy, solver_info
    
    def solve_with_classical_optimization(self, Q: np.ndarray) -> Tuple[np.ndarray, float, Dict]:
        """Solve using classical optimization methods."""

        n = len(Q)

        # Simple greedy approach for binary optimization
        # Since this is a minimization problem, we want to select nodes with most negative diagonal values
        start_time = time.time()

        # Get diagonal values (linear terms)
        diagonal_values = np.diag(Q)

        # Sort nodes by their linear contribution (most negative first for minimization)
        sorted_indices = np.argsort(diagonal_values)

        # Select approximately 30-70% of nodes (reasonable coverage)
        target_coverage = np.random.uniform(0.3, 0.7)
        num_to_select = int(target_coverage * n)

        # Create solution vector
        solution = np.zeros(n, dtype=int)
        selected_indices = sorted_indices[:num_to_select]
        solution[selected_indices] = 1

        # Calculate objective value
        objective_value = solution.T @ Q @ solution

        computation_time = time.time() - start_time

        solver_info = {
            'solver_type': 'classical_greedy',
            'computation_time': computation_time,
            'binary_objective': objective_value,
            'nodes_selected': num_to_select,
            'target_coverage': target_coverage
        }

        return solution, objective_value, solver_info
    
    def solve_qubo(self, Q: np.ndarray) -> Tuple[np.ndarray, float, Dict]:
        """
        Solve QUBO problem using the configured solver.
        
        Args:
            Q: QUBO matrix (in minimization form)
        
        Returns:
            Tuple of (solution vector, objective value, solver info)
        """
        
        if self.config.solver_type == "simulated_annealing" and DWAVE_AVAILABLE:
            return self.solve_with_simulated_annealing(Q)
        elif self.config.solver_type == "classical":
            return self.solve_with_classical_optimization(Q)
        else:
            # Fallback to classical if requested solver not available
            print(f"Solver {self.config.solver_type} not available, using classical fallback")
            return self.solve_with_classical_optimization(Q)


def optimize_pollination(
    field: Field,
    solver_config: Optional[SolverConfig] = None,
    formulation_params: Optional[Dict] = None
) -> OptimizationResult:
    """
    Main function to optimize pollination for a given field.
    
    Args:
        field: Field object to optimize
        solver_config: Configuration for the quantum solver
        formulation_params: Parameters for QUBO formulation
    
    Returns:
        OptimizationResult with optimal solution and metrics
    """
    
    start_time = time.time()
    
    # Initialize components
    formulator = QUBOFormulator(field)
    solver = QuantumSolver(solver_config)
    
    # Set default formulation parameters
    if formulation_params is None:
        formulation_params = {}
    
    # Create QUBO formulation
    Q, qubo_params = formulator.formulate_qubo(**formulation_params)
    
    # Solve the QUBO problem
    solution, objective_value, solver_info = solver.solve_qubo(Q)
    
    total_time = time.time() - start_time
    
    # Create optimization result
    result = OptimizationResult(
        field=field,
        optimal_solution=solution,
        objective_value=-objective_value,  # Convert back to maximization
        iterations=1,  # Will be updated by adaptive feedback
        computation_time=total_time,
        quantum_energy=solver_info.get('energy', objective_value),
        stress_adjustments={}  # Will be populated by adaptive feedback
    )
    
    return result
