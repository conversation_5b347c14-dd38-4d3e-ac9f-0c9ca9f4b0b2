"""
Simple demo for QEPO system without visualization dependencies.

This script demonstrates the core functionality of the Quantum-Enhanced 
Pollination Optimization system without requiring plotly or other 
visualization libraries.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from qepo.data.synthetic_generator import generate_field
from qepo.core.quantum_solver import optimize_pollination, SolverConfig
from qepo.core.adaptive_feedback import AdaptiveFeedbackEngine, AdaptiveConfig


def main():
    """Run simple QEPO demo."""
    
    print("🌸 QEPO Simple Demo")
    print("=" * 50)
    
    # Step 1: Generate synthetic field
    print("\n1. Generating synthetic field...")
    field = generate_field(
        grid_size=6,
        field_width=600,
        field_height=600,
        crop_diversity=True,
        stress_zones=True,
        seed=42
    )
    
    print(f"   Generated field with {len(field.nodes)} nodes and {len(field.edges)} edges")
    
    # Display field statistics
    stats = field.get_field_statistics()
    print(f"   Total yield potential: {stats['total_yield_potential']:.2f}")
    print(f"   Average stress factor: {stats['average_stress_factor']:.2f}")
    print(f"   Field area: {stats['field_area']:.0f} m²")
    
    # Show flowering stage distribution
    print("\n   Flowering stage distribution:")
    for stage, count in stats['nodes_by_flowering_stage'].items():
        print(f"     {stage}: {count} nodes")
    
    # Step 2: Run standard optimization
    print("\n2. Running standard quantum optimization...")
    
    solver_config = SolverConfig(
        solver_type="classical",
        max_iterations=50
    )
    
    formulation_params = {
        'yield_weight': 1.0,
        'movement_weight': 0.5,
        'stress_penalty_weight': 2.0,
        'coverage_constraint': True,
        'flowering_bonus': True,
        'spatial_clustering': True
    }
    
    standard_result = optimize_pollination(field, solver_config, formulation_params)
    
    print(f"   Coverage: {standard_result.get_coverage_percentage():.1f}%")
    print(f"   Yield improvement: {standard_result.get_yield_improvement():.1f}%")
    print(f"   Computation time: {standard_result.computation_time:.3f}s")
    print(f"   Selected nodes: {len(standard_result.get_pollinated_nodes())}")
    
    # Step 3: Run adaptive optimization
    print("\n3. Running adaptive optimization with bio-inspired feedback...")
    
    adaptive_config = AdaptiveConfig(
        max_iterations=3,
        stress_threshold=0.6,
        adaptation_rate=0.5
    )
    
    solver_config_adaptive = SolverConfig(solver_type="classical")
    
    adaptive_engine = AdaptiveFeedbackEngine(adaptive_config)
    adaptive_result = adaptive_engine.adaptive_optimize(field, solver_config_adaptive, formulation_params)
    
    print(f"   Adaptive coverage: {adaptive_result.get_coverage_percentage():.1f}%")
    print(f"   Adaptive yield improvement: {adaptive_result.get_yield_improvement():.1f}%")
    print(f"   Adaptive iterations: {adaptive_result.iterations}")
    print(f"   Total computation time: {adaptive_result.computation_time:.3f}s")
    
    # Get adaptation summary
    summary = adaptive_engine.get_adaptation_summary()
    if summary:
        print(f"   Adaptation improvement: {summary.get('improvement_percentage', 0):.1f}%")
        print(f"   Memory size: {summary.get('memory_size', 0)} nodes")
    
    # Step 4: Compare results
    print("\n4. Comparison Summary:")
    print(f"   Standard optimization:")
    print(f"     - Coverage: {standard_result.get_coverage_percentage():.1f}%")
    print(f"     - Yield improvement: {standard_result.get_yield_improvement():.1f}%")
    print(f"     - Computation time: {standard_result.computation_time:.3f}s")
    
    print(f"   Adaptive optimization:")
    print(f"     - Coverage: {adaptive_result.get_coverage_percentage():.1f}%")
    print(f"     - Yield improvement: {adaptive_result.get_yield_improvement():.1f}%")
    print(f"     - Computation time: {adaptive_result.computation_time:.3f}s")
    
    adaptive_advantage = (adaptive_result.get_yield_improvement() - 
                         standard_result.get_yield_improvement())
    print(f"   Adaptive advantage: {adaptive_advantage:.1f} percentage points")
    
    # Step 5: Analyze selected nodes
    print("\n5. Selected Nodes Analysis:")
    
    standard_nodes = set(standard_result.get_pollinated_nodes())
    adaptive_nodes = set(adaptive_result.get_pollinated_nodes())
    
    print(f"   Standard selected: {len(standard_nodes)} nodes")
    print(f"   Adaptive selected: {len(adaptive_nodes)} nodes")
    print(f"   Common selections: {len(standard_nodes & adaptive_nodes)} nodes")
    print(f"   Adaptive-only selections: {len(adaptive_nodes - standard_nodes)} nodes")
    
    # Show some selected nodes with their properties
    print("\n   Sample selected nodes (adaptive):")
    for i, node_id in enumerate(list(adaptive_nodes)[:5]):  # Show first 5
        node = field.nodes[node_id]
        print(f"     {node_id}: {node.crop_type.value}, {node.flowering_stage.value}, "
              f"yield={node.get_yield_potential():.2f}, stress={node.environmental_conditions.get_stress_factor():.2f}")
    
    # Step 6: Stress analysis
    print("\n6. Stress Analysis:")
    stressed_nodes = field.get_stressed_nodes(threshold=0.6)
    print(f"   Total stressed nodes: {len(stressed_nodes)}")
    
    if adaptive_result.stress_adjustments:
        print(f"   Nodes with stress adjustments: {len(adaptive_result.stress_adjustments)}")
        print("   Sample adjustments:")
        for i, (node_id, adjustment) in enumerate(list(adaptive_result.stress_adjustments.items())[:3]):
            print(f"     {node_id}: {adjustment:.3f}")
    
    print("\n" + "=" * 50)
    print("🎉 QEPO demo completed successfully!")
    print("\nKey achievements:")
    print(f"✅ Generated realistic {len(field.nodes)}-node agricultural field")
    print(f"✅ Formulated and solved quantum optimization problem")
    print(f"✅ Applied bio-inspired adaptive feedback mechanism")
    print(f"✅ Achieved {adaptive_result.get_yield_improvement():.1f}% yield improvement")
    
    print("\nNext steps:")
    print("1. Run the interactive dashboard: streamlit run qepo/visualization/dashboard.py")
    print("2. Experiment with different field sizes and parameters")
    print("3. Try different solver types (simulated_annealing if D-Wave is available)")
    
    return True


if __name__ == "__main__":
    main()
